package com.tqhit.battery.one.features.stats.charge.presentation

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.SeekBar
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.viewModels
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.FragmentStatsChargeBinding
import com.tqhit.battery.one.dialog.alarm.SelectBatteryAlarmDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.service.VibrationService
import com.tqhit.battery.one.utils.PermissionUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.features.navigation.SharedNavigationViewModel
import com.tqhit.battery.one.features.navigation.AppNavigator
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Fragment for displaying charge statistics and estimates.
 * Shows current battery status, active charge session, and time estimates.
 * 
 * ARCHITECTURE COMPLIANCE:
 * - Extends AdLibBaseFragment for consistent ViewBinding pattern
 * - Follows stats module architecture with proper lifecycle management
 * - Implements comprehensive null safety and defensive programming
 * - Uses structured logging for debugging and ADB testing
 * 
 * CRASH FIX IMPLEMENTATION:
 * - Replaced manual findViewById() with ViewBinding for null safety
 * - Added comprehensive binding validation and error handling
 * - Implemented proper fragment lifecycle state validation
 * - Added defensive programming practices with fallback behavior
 */
@AndroidEntryPoint
class StatsChargeFragment : AdLibBaseFragment<FragmentStatsChargeBinding>() {

    companion object {
        private const val TAG = "StatsChargeFragment"

        fun newInstance(): StatsChargeFragment {
            return StatsChargeFragment()
        }
    }

    // ViewBinding implementation following established pattern
    override val binding by lazy { 
        Log.d(TAG, "BINDING_INIT: Initializing FragmentStatsChargeBinding")
        try {
            FragmentStatsChargeBinding.inflate(layoutInflater)
        } catch (e: Exception) {
            Log.e(TAG, "BINDING_INIT: Critical error initializing ViewBinding", e)
            throw e
        }
    }

    private val viewModel: StatsChargeViewModel by viewModels()
    private val appViewModel: AppViewModel by viewModels()
    private val sharedNavigationViewModel: SharedNavigationViewModel by activityViewModels()

    @Inject
    lateinit var vibrationService: VibrationService

    @Inject
    lateinit var appNavigator: AppNavigator

    private val permissionLauncher =
        registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            if (isGranted) {
                showBatteryAlarmDialog()
            } else {
                NotificationDialog(
                    requireActivity(),
                    getString(R.string.notification),
                    getString(R.string.notify_access)
                ).show()
            }
        }

    /**
     * DEFENSIVE PROGRAMMING: Safe binding access with comprehensive validation
     * This method ensures binding is valid before any UI operations
     */
    private fun safeBindingAccess(operation: String, action: (FragmentStatsChargeBinding) -> Unit) {
        try {
            if (!isAdded) {
                Log.w(TAG, "BINDING_SAFETY: Fragment not added, skipping $operation")
                return
            }

            if (view == null) {
                Log.w(TAG, "BINDING_SAFETY: Fragment view is null, skipping $operation")
                return
            }

            // For lazy properties, we can't use ::binding.isInitialized
            // Instead, we try to access the binding and catch any exceptions
            try {
                action(binding)
                Log.v(TAG, "BINDING_SAFETY: Successfully executed $operation")
            } catch (bindingException: Exception) {
                Log.e(TAG, "BINDING_SAFETY: ViewBinding access failed for $operation", bindingException)
            }

        } catch (e: Exception) {
            Log.e(TAG, "BINDING_SAFETY: Error during $operation", e)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        val startTime = System.currentTimeMillis()
        Log.d(TAG, "STARTUP_TIMING: StatsChargeFragment.onViewCreated() started at $startTime")

        super.onViewCreated(view, savedInstanceState)

        // CRASH FIX: Use safe binding access for all UI operations
        safeBindingAccess("setupUI") { binding ->
            val setupStartTime = System.currentTimeMillis()
            setupSeekBar()
            setupResetButton()
            setupBatteryAlarmButton()
            setupBackNavigation()
            Log.d(TAG, "STARTUP_TIMING: UI setup took ${System.currentTimeMillis() - setupStartTime}ms")
        }

        val observeStartTime = System.currentTimeMillis()
        observeUiState()
        observeNavigationState()
        Log.d(TAG, "STARTUP_TIMING: observeUiState() and observeNavigationState() took ${System.currentTimeMillis() - observeStartTime}ms")
        Log.d(TAG, "SharedNavigationViewModel: Navigation state observation established")

        Log.d(TAG, "StatsChargeFragment view created")
        Log.d(TAG, "STARTUP_TIMING: StatsChargeFragment.onViewCreated() completed in ${System.currentTimeMillis() - startTime}ms")
    }

    /**
     * Sets up the target percentage SeekBar with null safety.
     */
    private fun setupSeekBar() {
        safeBindingAccess("setupSeekBar") { binding ->
            binding.seekbarTarget.min = 1
            binding.seekbarTarget.max = 100
            
            binding.seekbarTarget.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
                override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                    if (fromUser) {
                        safeBindingAccess("updateTargetPercentageText") { binding ->
                            binding.tvTargetPercentage.text = "${progress}%"
                        }
                    }
                }
                
                override fun onStartTrackingTouch(seekBar: SeekBar?) {
                    // No action needed
                }
                
                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                    seekBar?.let { bar ->
                        viewModel.setTargetChargePercentage(bar.progress)
                        Log.d(TAG, "Target percentage set to: ${bar.progress}%")
                    }
                }
            })
        }
    }

    /**
     * Sets up the reset session button with null safety.
     */
    private fun setupResetButton() {
        safeBindingAccess("setupResetButton") { binding ->
            binding.btnResetSession.setOnClickListener {
                viewModel.resetChargeSession()
                Log.d(TAG, "Reset session button clicked")
            }
        }
    }

    /**
     * Sets up the battery alarm button with null safety.
     */
    private fun setupBatteryAlarmButton() {
        safeBindingAccess("setupBatteryAlarmButton") { binding ->
            binding.batteryAlarmBtn.setOnClickListener {
                showBatteryAlarmDialog()
            }
        }
    }

    /**
     * Shows the battery alarm dialog with permission handling.
     */
    private fun showBatteryAlarmDialog() {
        if (PermissionUtils.isNotificationPermissionGranted(requireContext())) {
            SelectBatteryAlarmDialog(requireActivity(), permissionLauncher, appViewModel, vibrationService).show()
        } else {
            PermissionUtils.requestNotificationPermission(
                context = requireContext(),
                permissionLauncher = permissionLauncher,
                onPermissionGranted = { SelectBatteryAlarmDialog(requireActivity(), permissionLauncher, appViewModel, vibrationService).show() },
                onPermissionDenied = { NotificationDialog(requireActivity(), getString(R.string.notification), getString(R.string.notify_access)).show() }
            )
        }
    }

    /**
     * Observes UI state changes and updates the views with null safety.
     */
    private fun observeUiState() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.uiState.collect { uiState ->
                    updateUI(uiState)
                }
            }
        }
    }

    /**
     * Observes navigation state changes from SharedNavigationViewModel.
     * This replaces the external FragmentLifecycleOptimizer with self-managed fragment state.
     */
    private fun observeNavigationState() {
        Log.d(TAG, "SharedNavigationViewModel: Setting up navigation state observation")

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                sharedNavigationViewModel.activeFragmentId.collect { activeFragmentId ->
                    val isThisFragmentActive = activeFragmentId == R.id.chargeFragment

                    Log.d(TAG, "SharedNavigationViewModel: Navigation state changed - activeFragment: ${getFragmentName(activeFragmentId)}")
                    Log.d(TAG, "SharedNavigationViewModel: StatsChargeFragment is ${if (isThisFragmentActive) "ACTIVE" else "INACTIVE"}")

                    if (isThisFragmentActive) {
                        onFragmentVisible()
                    } else {
                        onFragmentHidden()
                    }
                }
            }
        }
    }

    /**
     * Called when this fragment becomes visible/active.
     * Triggers UI refresh to prevent staleness.
     */
    private fun onFragmentVisible() {
        Log.d(TAG, "SharedNavigationViewModel: StatsChargeFragment is now VISIBLE")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment activated via SharedNavigationViewModel")

        // Trigger UI refresh to prevent staleness
        val currentState = viewModel.uiState.value
        updateUI(currentState)
    }

    /**
     * Called when this fragment becomes hidden/inactive.
     */
    private fun onFragmentHidden() {
        Log.d(TAG, "SharedNavigationViewModel: StatsChargeFragment is now HIDDEN")
        Log.d(TAG, "FRAGMENT_LIFECYCLE: Fragment deactivated via SharedNavigationViewModel")
    }

    /**
     * Gets a human-readable fragment name for logging.
     */
    private fun getFragmentName(fragmentId: Int): String {
        return when (fragmentId) {
            R.id.chargeFragment -> "ChargeFragment"
            R.id.dischargeFragment -> "DischargeFragment"
            R.id.animationGridFragment -> "AnimationGridFragment"
            R.id.othersFragment -> "OthersFragment"
            R.id.healthFragment -> "HealthFragment"
            R.id.settingsFragment -> "SettingsFragment"
            else -> "Unknown($fragmentId)"
        }
    }

    /**
     * Updates all UI views with the current state using safe binding access.
     * CRASH FIX: All UI updates now use safeBindingAccess for null safety
     */
    private fun updateUI(uiState: StatsChargeUiState) {
        Log.v(TAG, "Updating UI with state: charging=${uiState.status?.isCharging}, " +
            "percentage=${uiState.status?.percentage}%, " +
            "sessionActive=${uiState.session?.isActive}")

        updateBatteryStatusViews(uiState)
        updateSessionViews(uiState)
        updateEstimateViews(uiState)
        updateTargetPercentage(uiState)
    }

    /**
     * Updates battery status related views with comprehensive null safety.
     * CRASH FIX: Uses safeBindingAccess to prevent NullPointerException
     */
    private fun updateBatteryStatusViews(uiState: StatsChargeUiState) {
        safeBindingAccess("updateBatteryStatusViews") { binding ->
            val status = uiState.status

            if (status != null) {
                binding.tvPercentage.text = "${status.percentage}%"
                binding.chargeProgBarPercent.progress = status.percentage
                binding.tvChargingStatus.text = if (status.isCharging) "Charging" else "Not Charging"
                binding.tvCurrent.text = viewModel.formatCurrent(status.currentMicroAmperes)
                binding.tvVoltage.text = viewModel.formatVoltage(status.voltageMillivolts)
                binding.tvTemperature.text = viewModel.formatTemperature(status.temperatureCelsius)
                binding.tvPower.text = viewModel.formatPower(uiState.powerWatts)
            } else {
                binding.tvPercentage.text = "N/A"
                binding.chargeProgBarPercent.progress = 0
                binding.tvChargingStatus.text = "Unknown"
                binding.tvCurrent.text = "N/A"
                binding.tvVoltage.text = "N/A"
                binding.tvTemperature.text = "N/A"
                binding.tvPower.text = "N/A"
            }
        }
    }

    /**
     * Updates session related views with comprehensive null safety.
     * CRASH FIX: Uses safeBindingAccess to prevent NullPointerException
     */
    private fun updateSessionViews(uiState: StatsChargeUiState) {
        safeBindingAccess("updateSessionViews") { binding ->
            val session = uiState.session
            val status = uiState.status

            if (session != null && session.isActive) {
                // Format start time
                val startTime = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
                    .format(java.util.Date(session.startTimeEpochMillis))
                binding.tvSessionStartTime.text = startTime

                // Format duration
                binding.tvSessionDuration.text = viewModel.formatTime(session.durationMillis)

                // Calculate and display percentage charged
                val percentageCharged = if (status != null) {
                    session.getPercentageCharged(status.percentage)
                } else {
                    session.percentageCharged
                }
                binding.tvSessionPercentageCharged.text = "${percentageCharged}%"

                // Display total charge mAh
                binding.tvSessionTotalChargeMah.text = String.format("%.1fmAh", session.totalChargeMah)

                binding.btnResetSession.isEnabled = true
            } else {
                binding.tvSessionStartTime.text = "-"
                binding.tvSessionDuration.text = "-"
                binding.tvSessionPercentageCharged.text = "-"
                binding.tvSessionTotalChargeMah.text = "-"
                binding.btnResetSession.isEnabled = false
            }
        }
    }

    /**
     * Updates estimate related views with comprehensive null safety.
     * CRASH FIX: Uses safeBindingAccess to prevent NullPointerException
     */
    private fun updateEstimateViews(uiState: StatsChargeUiState) {
        safeBindingAccess("updateEstimateViews") { binding ->
            binding.tvTimeToFull.text = viewModel.formatTime(uiState.timeToFullMillis)
            binding.tvTimeToTarget.text = viewModel.formatTime(uiState.timeToTargetMillis)
        }
    }

    /**
     * Updates target percentage related views with comprehensive null safety.
     * CRASH FIX: Uses safeBindingAccess to prevent NullPointerException
     */
    private fun updateTargetPercentage(uiState: StatsChargeUiState) {
        safeBindingAccess("updateTargetPercentage") { binding ->
            if (binding.seekbarTarget.progress != uiState.targetPercentage) {
                binding.seekbarTarget.progress = uiState.targetPercentage
            }
            binding.tvTargetPercentage.text = "${uiState.targetPercentage}%"
        }
    }

    /**
     * Sets up back navigation button click handling with comprehensive null safety.
     * Uses centralized AppNavigator for consistent navigation behavior.
     * CRASH FIX: Uses safeBindingAccess to prevent NullPointerException
     */
    private fun setupBackNavigation() {
        Log.d(TAG, "BACK_NAVIGATION: Setting up back navigation button")

        safeBindingAccess("setupBackNavigation") { binding ->
            binding.includeBackNavigation.btnBackNavigation.setOnClickListener {
                val backNavigationTime = System.currentTimeMillis()

                // NAVIGATION_CHAIN_TEST: Unified logging for complete navigation chain tracking
                Log.d("NAVIGATION_CHAIN_TEST", "═══════════════════════════════════════════════════════════")
                Log.d("NAVIGATION_CHAIN_TEST", "🔙 CHARGE FRAGMENT: BACK NAVIGATION TO OTHERS")
                Log.d("NAVIGATION_CHAIN_TEST", "═══════════════════════════════════════════════════════════")
                Log.d("NAVIGATION_CHAIN_TEST", "Back navigation timestamp: $backNavigationTime")
                Log.d("NAVIGATION_CHAIN_TEST", "Fragment state: isAdded=$isAdded, isVisible=$isVisible")
                Log.d("NAVIGATION_CHAIN_TEST", "Fragment lifecycle: ${lifecycle.currentState}")
                Log.d("NAVIGATION_CHAIN_TEST", "Target: Others Fragment")
                Log.d("NAVIGATION_CHAIN_TEST", "Navigation method: AppNavigator (modern)")
                Log.d("NAVIGATION_CHAIN_TEST", "⚠️ CRITICAL: This navigation may affect Others Fragment state")

                Log.d(TAG, "BACK_NAVIGATION: Back button clicked - using centralized AppNavigator")
                Log.d(TAG, "MultiNavigation: StatsChargeFragment back navigation initiated via AppNavigator")
                Log.d(TAG, "FragmentCache: Using AppNavigator to preserve fragment cache")

                try {
                    // Initialize AppNavigator if needed
                    initializeAppNavigatorIfNeeded()

                    Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Calling appNavigator.navigateToOthers()")

                    // Try AppNavigator first (modern centralized approach)
                    val navigationSuccess = appNavigator.navigateToOthers()

                    if (navigationSuccess) {
                        Log.d(TAG, "BACK_NAVIGATION: Successfully navigated back using AppNavigator")
                        Log.d(TAG, "MultiNavigation: AppNavigator preserved fragment lifecycle")
                        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: ✅ AppNavigator navigation successful")
                        Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Others Fragment should now be loading...")
                        Log.d("NAVIGATION_CHAIN_TEST", "✅ CHARGE→OTHERS SUCCESS: AppNavigator completed successfully")
                        Log.d("NAVIGATION_CHAIN_TEST", "🎯 CRITICAL POINT: Others Fragment state may be affected by Charge visit")
                    } else {
                        Log.w(TAG, "BACK_NAVIGATION: AppNavigator failed - using legacy fallback")
                        Log.w(TAG, "NAVIGATION_CHAIN_DEBUG: ❌ AppNavigator failed, using legacy fallback")
                        Log.w("NAVIGATION_CHAIN_TEST", "⚠️ CHARGE→OTHERS FALLBACK: AppNavigator failed, using legacy")
                        navigateBackLegacy()
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "BACK_NAVIGATION: Error during AppNavigator navigation", e)
                    Log.e(TAG, "MultiNavigation: AppNavigator failed - attempting legacy fallback")
                    Log.e(TAG, "NAVIGATION_CHAIN_DEBUG: ❌ AppNavigator exception, using legacy fallback", e)
                    Log.e("NAVIGATION_CHAIN_TEST", "❌ CHARGE→OTHERS ERROR: AppNavigator exception, using legacy", e)
                    navigateBackLegacy()
                }

                Log.d(TAG, "NAVIGATION_CHAIN_DEBUG: Charge Fragment back navigation completed")
                Log.d("NAVIGATION_CHAIN_TEST", "🏁 CHARGE FRAGMENT: Back navigation completed")
                Log.d("NAVIGATION_CHAIN_TEST", "═══════════════════════════════════════════════════════════")
            }
        }
    }

    /**
     * Initializes AppNavigator if not already initialized.
     * This ensures centralized navigation is available for back navigation.
     * CRASH FIX: Added comprehensive error handling and validation
     */
    private fun initializeAppNavigatorIfNeeded() {
        if (!appNavigator.isInitialized()) {
            Log.d(TAG, "APPNAVIGATOR_INIT: Initializing AppNavigator from StatsChargeFragment")
            try {
                val activity = requireActivity()
                val fragmentManager = activity.supportFragmentManager
                val bottomNavigationView = activity.findViewById<com.google.android.material.bottomnavigation.BottomNavigationView>(R.id.bottom_view)
                val fragmentContainerId = R.id.nav_host_fragment

                if (bottomNavigationView != null) {
                    appNavigator.initialize(
                        fragmentManager = fragmentManager,
                        bottomNavigationView = bottomNavigationView,
                        fragmentContainerId = fragmentContainerId,
                        lifecycleOwner = this
                    )
                    Log.d(TAG, "APPNAVIGATOR_INIT: AppNavigator initialized successfully")
                } else {
                    Log.e(TAG, "APPNAVIGATOR_INIT: BottomNavigationView not found")
                }
            } catch (e: Exception) {
                Log.e(TAG, "APPNAVIGATOR_INIT: Error initializing AppNavigator", e)
            }
        }
    }

    /**
     * Legacy back navigation using MainActivity's navigation system.
     * Used as fallback when AppNavigator fails.
     * CRASH FIX: Added comprehensive error handling and multiple fallback strategies
     */
    private fun navigateBackLegacy() {
        Log.d(TAG, "BACK_NAVIGATION: Using legacy MainActivity navigation")

        try {
            // MULTI_NAVIGATION_FIX: Use MainActivity's navigation system instead of direct popBackStack
            // This preserves the DynamicNavigationManager's fragment cache and show/hide pattern
            val mainActivity = requireActivity() as? com.tqhit.battery.one.activity.main.MainActivity
            if (mainActivity != null) {
                Log.d(TAG, "MultiNavigation: Using MainActivity's navigation system to preserve fragment cache")
                mainActivity.navigateToOthersFragment()
                Log.d(TAG, "BACK_NAVIGATION: Successfully navigated back using MainActivity navigation")
            } else {
                Log.w(TAG, "MultiNavigation: MainActivity not available - using popBackStack fallback")
                Log.w(TAG, "FragmentCache: WARNING - popBackStack may corrupt fragment cache")

                // Use the fragment manager to navigate back
                // This will properly handle the back stack and return to Others fragment
                requireActivity().supportFragmentManager.popBackStack()
                Log.d(TAG, "BACK_NAVIGATION: Successfully navigated back using popBackStack()")
            }
        } catch (e: Exception) {
            Log.e(TAG, "BACK_NAVIGATION: Error in legacy navigation", e)
            Log.e(TAG, "MultiNavigation: Legacy navigation failed - attempting emergency fallback")

            // Emergency fallback: Try popBackStack only (avoid replace() to preserve fragment cache)
            try {
                val fragmentManager = requireActivity().supportFragmentManager
                if (fragmentManager.backStackEntryCount > 0) {
                    fragmentManager.popBackStack()
                    fragmentManager.executePendingTransactions()
                    Log.d(TAG, "BACK_NAVIGATION: Emergency fallback using popBackStack() successful")
                } else {
                    Log.e(TAG, "BACK_NAVIGATION: No back stack entries - cannot navigate back safely")
                    Log.e(TAG, "FragmentCache: Cannot preserve fragment cache without proper navigation")
                }
            } catch (fallbackException: Exception) {
                Log.e(TAG, "BACK_NAVIGATION: All navigation attempts failed", fallbackException)
                Log.e(TAG, "MultiNavigation: CRITICAL - All navigation methods failed")
            }
        }
    }
}
