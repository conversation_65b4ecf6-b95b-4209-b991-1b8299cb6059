package com.tqhit.battery.one.features.navigation

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.tqhit.battery.one.R
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import io.mockk.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

/**
 * Performance tests for DynamicNavigationManager fragment caching optimization.
 */
class DynamicNavigationManagerPerformanceTest {

    private lateinit var dynamicNavigationManager: DynamicNavigationManager
    private lateinit var mockCoreBatteryStatsProvider: CoreBatteryStatsProvider
    private lateinit var mockFragmentManager: FragmentManager
    private lateinit var mockBottomNavigationView: BottomNavigationView
    private lateinit var mockLifecycleOwner: LifecycleOwner
    private lateinit var mockTransaction: FragmentTransaction

    @Before
    fun setup() {
        mockCoreBatteryStatsProvider = mockk()
        mockFragmentManager = mockk()
        mockBottomNavigationView = mockk()
        mockLifecycleOwner = mockk()
        mockTransaction = mockk()

        // Mock the battery status flow
        every { mockCoreBatteryStatsProvider.coreBatteryStatusFlow } returns MutableStateFlow(null)
        every { mockCoreBatteryStatsProvider.getCurrentStatus() } returns null

        // Mock FragmentManager
        every { mockFragmentManager.beginTransaction() } returns mockTransaction
        every { mockTransaction.setCustomAnimations(any(), any()) } returns mockTransaction
        every { mockTransaction.add(any(), any<Fragment>(), any()) } returns mockTransaction
        every { mockTransaction.show(any()) } returns mockTransaction
        every { mockTransaction.hide(any()) } returns mockTransaction
        every { mockTransaction.commitAllowingStateLoss() } returns 0

        // Mock BottomNavigationView
        val mockMenu = mockk<android.view.Menu>()
        every { mockBottomNavigationView.menu } returns mockMenu
        every { mockBottomNavigationView.selectedItemId } returns R.id.chargeFragment
        every { mockBottomNavigationView.selectedItemId = any() } just Runs
        every { mockMenu.size() } returns 5
        every { mockMenu.getItem(any()) } returns mockk {
            every { itemId } returns R.id.chargeFragment
            every { isVisible } returns true
            every { isVisible = any() } just Runs
        }

        // Mock LifecycleOwner
        every { mockLifecycleOwner.lifecycleScope } returns mockk {
            every { launch(any(), any(), any()) } returns mockk()
        }

        dynamicNavigationManager = DynamicNavigationManager(mockCoreBatteryStatsProvider)
    }

    @Test
    fun `fragment caching reduces creation count on repeated navigation`() = runTest {
        // Initialize the navigation manager
        dynamicNavigationManager.initialize(
            fragmentManager = mockFragmentManager,
            bottomNavigationView = mockBottomNavigationView,
            fragmentContainerId = R.id.nav_host_fragment,
            lifecycleOwner = mockLifecycleOwner
        )

        // Navigate to charge fragment multiple times
        repeat(5) {
            dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)
        }

        // Navigate to discharge fragment multiple times
        repeat(5) {
            dynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment)
        }

        // Navigate back to charge fragment
        repeat(3) {
            dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)
        }

        val stats = dynamicNavigationManager.getPerformanceStats()
        
        // Verify that fragments are cached and reused
        assertTrue("Performance stats should contain cache information", stats.contains("Cached:"))
        assertTrue("Performance stats should contain creation count", stats.contains("Created:"))
        assertTrue("Performance stats should contain cache hits", stats.contains("Cache Hits:"))
        
        // Should have created only 2 fragments (charge and discharge) despite 13 navigation calls
        assertTrue("Should show fragment caching is working", stats.contains("Created: 2"))
    }

    @Test
    fun `navigation to same fragment is optimized`() = runTest {
        dynamicNavigationManager.initialize(
            fragmentManager = mockFragmentManager,
            bottomNavigationView = mockBottomNavigationView,
            fragmentContainerId = R.id.nav_host_fragment,
            lifecycleOwner = mockLifecycleOwner
        )

        // Navigate to the same fragment multiple times
        val result1 = dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)
        val result2 = dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)
        val result3 = dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)

        // All navigation calls should return true (handled)
        assertTrue("First navigation should be handled", result1)
        assertTrue("Second navigation should be handled", result2)
        assertTrue("Third navigation should be handled", result3)

        // Verify that fragment transaction is not called for same fragment navigation
        verify(exactly = 1) { mockFragmentManager.beginTransaction() }
    }

    @Test
    fun `shared navigation viewmodel tracks fragment states`() = runTest {
        dynamicNavigationManager.initialize(
            fragmentManager = mockFragmentManager,
            bottomNavigationView = mockBottomNavigationView,
            fragmentContainerId = R.id.nav_host_fragment,
            lifecycleOwner = mockLifecycleOwner
        )

        // Navigate between different fragments
        dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)
        dynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment)
        dynamicNavigationManager.handleUserNavigation(R.id.healthFragment)

        val stats = dynamicNavigationManager.getPerformanceStats()

        // Verify lifecycle stats are included (now managed by SharedNavigationViewModel)
        assertTrue("Stats should include lifecycle information", stats.contains("Fragment Lifecycle Stats"))
    }

    @Test
    fun `performance stats provide meaningful metrics`() = runTest {
        dynamicNavigationManager.initialize(
            fragmentManager = mockFragmentManager,
            bottomNavigationView = mockBottomNavigationView,
            fragmentContainerId = R.id.nav_host_fragment,
            lifecycleOwner = mockLifecycleOwner
        )

        // Perform various navigation operations
        dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)
        dynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment)
        dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment) // Cache hit
        dynamicNavigationManager.handleUserNavigation(R.id.animationGridFragment)

        val stats = dynamicNavigationManager.getPerformanceStats()
        
        // Verify all expected metrics are present
        assertTrue("Should show cached fragments", stats.contains("Cached:"))
        assertTrue("Should show created count", stats.contains("Created:"))
        assertTrue("Should show cache hits", stats.contains("Cache Hits:"))
        assertTrue("Should show hit rate", stats.contains("Hit Rate:"))
        
        // Verify reasonable values
        assertTrue("Should have created 3 fragments", stats.contains("Created: 3"))
        assertTrue("Should have 1 cache hit", stats.contains("Cache Hits: 1"))
    }

    @Test
    fun `fragment cache is cleared on reinitialization`() = runTest {
        // First initialization
        dynamicNavigationManager.initialize(
            fragmentManager = mockFragmentManager,
            bottomNavigationView = mockBottomNavigationView,
            fragmentContainerId = R.id.nav_host_fragment,
            lifecycleOwner = mockLifecycleOwner
        )

        // Create some fragments
        dynamicNavigationManager.handleUserNavigation(R.id.chargeFragment)
        dynamicNavigationManager.handleUserNavigation(R.id.dischargeFragment)

        val statsAfterFirst = dynamicNavigationManager.getPerformanceStats()
        assertTrue("Should have cached fragments", statsAfterFirst.contains("Cached: 2"))

        // Reinitialize (simulating activity recreation)
        dynamicNavigationManager.initialize(
            fragmentManager = mockFragmentManager,
            bottomNavigationView = mockBottomNavigationView,
            fragmentContainerId = R.id.nav_host_fragment,
            lifecycleOwner = mockLifecycleOwner
        )

        val statsAfterReinit = dynamicNavigationManager.getPerformanceStats()
        assertTrue("Cache should be cleared after reinitialization", statsAfterReinit.contains("Cached: 0"))
        assertTrue("Creation count should be reset", statsAfterReinit.contains("Created: 0"))
        assertTrue("Cache hits should be reset", statsAfterReinit.contains("Cache Hits: 0"))
    }
}
