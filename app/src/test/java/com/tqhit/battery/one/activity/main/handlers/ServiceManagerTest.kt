package com.tqhit.battery.one.activity.main.handlers

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.tqhit.battery.one.features.stats.discharge.domain.AppLifecycleManager
import com.tqhit.battery.one.features.stats.discharge.domain.AppState
import com.tqhit.battery.one.features.stats.discharge.service.EnhancedDischargeTimerServiceHelper
import com.tqhit.battery.one.features.stats.notifications.UnifiedBatteryNotificationServiceHelper
import io.mockk.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

/**
 * Unit tests for ServiceManager
 * Tests foreground context detection and Android 12+ background service restrictions handling
 */
@ExperimentalCoroutinesApi
@RunWith(RobolectricTestRunner::class)
class ServiceManagerTest {

    private lateinit var serviceManager: ServiceManager
    private lateinit var mockUnifiedServiceHelper: UnifiedBatteryNotificationServiceHelper
    private lateinit var mockDischargeServiceHelper: EnhancedDischargeTimerServiceHelper
    private lateinit var mockAppLifecycleManager: AppLifecycleManager
    private lateinit var mockContext: Context
    private lateinit var mockLifecycleOwner: LifecycleOwner

    private val appStateFlow = MutableStateFlow(AppState.FOREGROUND)

    @Before
    fun setUp() {
        mockUnifiedServiceHelper = mockk(relaxed = true)
        mockDischargeServiceHelper = mockk(relaxed = true)
        mockAppLifecycleManager = mockk(relaxed = true)
        mockContext = mockk(relaxed = true)
        mockLifecycleOwner = mockk(relaxed = true)

        every { mockAppLifecycleManager.appState } returns appStateFlow

        serviceManager = ServiceManager(
            mockUnifiedServiceHelper,
            mockDischargeServiceHelper,
            mockAppLifecycleManager
        )
    }

    @Test
    fun `startCriticalServices should start services when app is in foreground`() = runTest {
        // Arrange
        appStateFlow.value = AppState.FOREGROUND

        // Act
        serviceManager.startCriticalServices(mockContext, mockLifecycleOwner)

        // Assert
        verify { mockUnifiedServiceHelper.startService() }
        verify { mockDischargeServiceHelper.startService() }
    }

    @Test
    fun `startCriticalServices should defer startup when app is in background`() = runTest {
        // Arrange
        appStateFlow.value = AppState.BACKGROUND

        // Act
        serviceManager.startCriticalServices(mockContext, mockLifecycleOwner)

        // Assert
        // Services should not be started immediately when in background
        verify(exactly = 0) { mockUnifiedServiceHelper.startService() }
        verify(exactly = 0) { mockDischargeServiceHelper.startService() }
    }

    @Test
    fun `ensureCriticalServicesRunning should restart services when app is in foreground`() = runTest {
        // Arrange
        appStateFlow.value = AppState.FOREGROUND
        every { mockUnifiedServiceHelper.isServiceRunning() } returns false
        every { mockDischargeServiceHelper.isServiceRunning() } returns false

        // Act
        serviceManager.ensureCriticalServicesRunning(mockContext, mockLifecycleOwner)

        // Assert
        verify { mockUnifiedServiceHelper.startService() }
        verify { mockDischargeServiceHelper.startService() }
    }

    @Test
    fun `ensureCriticalServicesRunning should skip restart when app is in background`() = runTest {
        // Arrange
        appStateFlow.value = AppState.BACKGROUND
        every { mockUnifiedServiceHelper.isServiceRunning() } returns false
        every { mockDischargeServiceHelper.isServiceRunning() } returns false

        // Act
        serviceManager.ensureCriticalServicesRunning(mockContext, mockLifecycleOwner)

        // Assert
        // Services should not be restarted when in background
        verify(exactly = 0) { mockUnifiedServiceHelper.startService() }
        verify(exactly = 0) { mockDischargeServiceHelper.startService() }
    }

    @Test
    fun `ensureCriticalServicesRunning should not restart running services`() = runTest {
        // Arrange
        appStateFlow.value = AppState.FOREGROUND
        every { mockUnifiedServiceHelper.isServiceRunning() } returns true
        every { mockDischargeServiceHelper.isServiceRunning() } returns true

        // Act
        serviceManager.ensureCriticalServicesRunning(mockContext, mockLifecycleOwner)

        // Assert
        verify(exactly = 0) { mockUnifiedServiceHelper.startService() }
        verify(exactly = 0) { mockDischargeServiceHelper.startService() }
    }

    @Test
    fun `getServiceStatus should return correct status information`() {
        // Arrange
        every { mockUnifiedServiceHelper.isServiceRunning() } returns true
        every { mockDischargeServiceHelper.isServiceRunning() } returns false

        // Act
        val status = serviceManager.getServiceStatus()

        // Assert
        assert(status.contains("UnifiedBatteryNotificationService: Running"))
        assert(status.contains("EnhancedDischargeTimerService: Stopped"))
    }

    @Test
    fun `getServiceStatus should handle exceptions gracefully`() {
        // Arrange
        every { mockUnifiedServiceHelper.isServiceRunning() } throws RuntimeException("Test error")

        // Act
        val status = serviceManager.getServiceStatus()

        // Assert
        assert(status.contains("Error retrieving status"))
    }

    @Test
    fun `performServiceHealthCheck should log service issues`() {
        // Arrange
        every { mockUnifiedServiceHelper.isServiceRunning() } returns false
        every { mockDischargeServiceHelper.isServiceRunning() } returns false

        // Act
        serviceManager.performServiceHealthCheck(mockContext)

        // Assert
        verify { mockUnifiedServiceHelper.isServiceRunning() }
        verify { mockDischargeServiceHelper.isServiceRunning() }
    }
}
