#!/bin/bash

# Android Kotlin Project Setup Script
# This script sets up the development environment for the TJ_BatteryOne Android app

set -e

echo "=========================================="
echo "Setting up Android Kotlin Development Environment"
echo "=========================================="

# Update system packages
echo "📦 Updating system packages..."
sudo apt-get update -qq

# Install Java 17 (required for this Android project)
echo "☕ Installing Java 17..."
sudo apt-get install -y openjdk-17-jdk

# Set JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
echo "export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64" >> $HOME/.profile

# Install Android SDK dependencies
echo "📱 Installing Android SDK dependencies..."
sudo apt-get install -y wget unzip curl

# Create Android SDK directory
ANDROID_HOME="$HOME/android-sdk"
mkdir -p "$ANDROID_HOME"
export ANDROID_HOME="$ANDROID_HOME"
echo "export ANDROID_HOME=$ANDROID_HOME" >> $HOME/.profile

# Download and install Android SDK Command Line Tools
echo "🔧 Installing Android SDK Command Line Tools..."
cd "$ANDROID_HOME"
wget -q https://dl.google.com/android/repository/commandlinetools-linux-11076708_latest.zip
unzip -q commandlinetools-linux-11076708_latest.zip
rm commandlinetools-linux-11076708_latest.zip

# Create proper directory structure
mkdir -p cmdline-tools/latest
mv cmdline-tools/* cmdline-tools/latest/ 2>/dev/null || true

# Add Android tools to PATH
export PATH="$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:$PATH"
echo "export PATH=\"$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools:\$PATH\"" >> $HOME/.profile

# Accept Android SDK licenses
echo "📋 Accepting Android SDK licenses..."
yes | sdkmanager --licenses > /dev/null 2>&1

# Install required Android SDK components
echo "📲 Installing Android SDK components..."
sdkmanager "platform-tools" "platforms;android-35" "platforms;android-24" "build-tools;35.0.0" > /dev/null

# Install Android emulator (for testing)
echo "🎮 Installing Android Emulator..."
sdkmanager "emulator" "system-images;android-35;google_apis;x86_64" > /dev/null

# Verify Java installation
echo "✅ Verifying Java installation..."
java -version

# Return to project directory
cd /mnt/persist/workspace

# Verify Gradle wrapper
echo "🔨 Verifying Gradle wrapper..."
if [ -f "./gradlew" ]; then
    chmod +x ./gradlew
    echo "Gradle wrapper found and made executable"
else
    echo "❌ Gradle wrapper not found!"
    exit 1
fi

# Clean and build the project to verify setup
echo "🏗️ Building project to verify setup..."
./gradlew clean

# Download dependencies
echo "📦 Downloading project dependencies..."
./gradlew dependencies > /dev/null

echo "✅ Android development environment setup completed!"
echo "📋 Ready to run tests..."